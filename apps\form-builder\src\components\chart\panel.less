.chart-panel {
  position: relative;
  box-sizing: border-box;
  border: 1px solid transparent;
  transform: translate3d(0,0,0); // 开启硬件加速，防止出现边框虚影
  will-change: transform;

  .icons {
    position: absolute;
    top: -18px;
    right: -1px;
    display: flex;
    align-items: center;
    padding: 2px 4px;
    background-color: blue;
    color: #fff;

    svg {
      margin-left: 4px;
      cursor: pointer;
    }
  }
}

.panel-selected {
  border-color: blue;
}
