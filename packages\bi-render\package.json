{"name": "pb-bi-render", "version": "0.0.4", "description": "", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/typing/index.d.ts", "files": ["dist", "src"], "scripts": {"dev": "tsup src/index.ts --watch", "setup": "tsup src/index.ts", "build": "tsup src/index.ts", "lint:js": "eslint src/**/*.{js,jsx,ts,tsx}", "lint:css": "stylelint --allow-empty-input 'src/**/*.{css,less}'", "lint:format": "prettier --check *.md *.json src/**/*.{js,jsx,ts,tsx,css,less,md,json}", "lint:typing": "tsc --noEmit", "test": "echo \"Error: no test specified\" && exit 1", "patch": "pnpm build && npm version patch && npm publish"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@pb-renders/config-tsconfig": "workspace:^", "@pb-renders/config-tsup": "workspace:^", "@types/react": "^18.0.28"}, "peerDependencies": {"@ant-design/icons": "^5.5.2", "react": "^18.0.0", "react-dom": "^18.0.0", "echarts": "^5"}}