import type { ClientRect } from '@dnd-kit/core';
import type { Schema } from 'pb-form-render';
import { create } from 'zustand';

interface RendererStore {
  schemas: Schema[];
  activeId: string;
  detectingId: string;
  isDragging: boolean;
  setIsDragging: (isDragging: boolean) => void;
  setDetectingId: (id: string) => void;
  setActiveId: (id: string) => void;
  setSchemas: (schemas: Schema[]) => void;
  getSchemas: () => Schema[];
  schemaIds: (string | { id: string; children: string[] })[];
  setSchemaIds: (schemaIds: (string | { id: string; children: string[] })[]) => void;
  getSchemaIds: () => (string | { id: string; children: string[] })[];
}

const useRendererStore = create<RendererStore>((set, get) => ({
  schemas: [],
  activeId: '',
  detectingId: '',
  isDragging: false,
  setIsDragging: (isDragging: boolean) => {
    set(() => ({ isDragging }));
  },
  setActiveId: (id: string) => {
    set(() => ({ activeId: id }));
  },
  setDetectingId: (id: string) => {
    set(() => ({ detectingId: id }));
  },
  getSchemas: () => {
    return get().schemas;
  },
  setSchemas: (schemas: Schema[]) => {
    set(() => ({ schemas: [...schemas] }));
  },
  schemaIds: [],
  getSchemaIds: () => {
    return get().schemaIds;
  },
  setSchemaIds: (schemaIds: (string | { id: string; children: string[] })[]) => {
    set(() => ({ schemaIds: [...schemaIds] }));
  },
}));

export default useRendererStore;
