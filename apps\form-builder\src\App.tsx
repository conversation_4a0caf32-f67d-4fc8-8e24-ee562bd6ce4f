import type { Active, CollisionDetection, DragEndEvent, Over } from '@dnd-kit/core';
import { closestCenter, DndContext, pointerWithin } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import type { Schema } from 'pb-form-render';
import type {
  FormItemSchema,
  FormListSchema,
  RowSchema,
} from 'pb-form-render/dist/typing/types';
import { v4 as uuid } from 'uuid';

import type { ContainerID } from './components/dnd/drag-item';
import MainContent from './components/renderer/content';
import { defaultSchema } from './constant/schema';
import Layout from './layout';
import { HeaderContent } from './layout/headerContent';
import SideContent from './layout/sideContent';
import { useRendererStore } from './store';

function addIdByPath(
  ids: (string | ContainerID)[],
  idPath: number[],
  id: string | ContainerID,
) {
  const newIds = [...ids];
  const target = newIds[idPath[0]];
  if (typeof target === 'string') {
    newIds.splice(idPath[0], 0, id);
  } else {
    if (!idPath[1]) newIds.splice(idPath[0], 0, id);
    else target.children.splice(idPath[1], 0, id as string);
  }
  console.log(newIds, 'add id');
  return newIds;
}

function deleteIdByPath(ids: (string | ContainerID)[], idPath: number[]) {
  const newIds = [...ids];
  const target = newIds[idPath[0]];
  if (typeof target === 'string') {
    newIds.splice(idPath[0], 1);
  } else {
    target.children.splice(idPath[1], 1);
  }
  console.log(newIds, 'delete id');
  return newIds;
}

function updateSchemaIds(
  ids: (string | ContainerID)[],
  idPath: number[],
  id?: string | ContainerID,
) {
  if (id) {
    return addIdByPath(ids, idPath, id);
  } else {
    return deleteIdByPath(ids, idPath);
  }
}

function updateSchemas(schemas: Schema[], idPath: number[], schema?: Schema) {
  const newSchemas = [...schemas];
  if (schema) {
    if (idPath.length > 1) {
      (newSchemas[idPath[0]] as RowSchema | FormListSchema).children.splice(
        idPath[1],
        0,
        schema as FormItemSchema,
      );
    } else {
      newSchemas.splice(idPath[0], 0, schema);
    }
  } else {
    if (idPath.length > 1) {
      (newSchemas[idPath[0]] as RowSchema | FormListSchema).children.splice(
        idPath[1],
        1,
      );
    } else {
      newSchemas.splice(idPath[0], 1);
    }
  }
  console.log(newSchemas, 'new schemas');
  return newSchemas;
}

function getPathById(ids: (string | ContainerID)[], id: string, sortType: string) {
  const path: number[] = [];
  if (sortType === 'container') {
    const index = ids.findIndex((item) => {
      if (typeof item === 'object') {
        const cIndex = item.children.findIndex((child) => child === id);
        if (cIndex > -1) {
          path.push(cIndex);
          return true;
        } else {
          return item.id === id;
        }
      }
    });
    path.unshift(index);
  } else {
    const index = ids.findIndex((item) => item === id);
    path.push(index);
  }
  return path;
}
// 自定义碰撞检测
const customCollisionDetection: CollisionDetection = (args) => {
  const pointerCollisions = pointerWithin(args);

  if (pointerCollisions.length > 0) {
    return pointerCollisions;
  }

  return closestCenter(args);
};

const moveContainerItemToRoot = (
  items: any[],
  newIndexPath: number[],
  oldIndexPath: number[], // > 1
) => {
  const container = items[oldIndexPath[0]];
  const old = container.children[oldIndexPath[1]];
  container.children.splice(oldIndexPath[1], 1);
  items.splice(newIndexPath[0] + 1, 0, old);
  return items;
};

const moveItemToContainer = (
  items: any[],
  newIndexPath: number[], // > 1
  oldIndexPath: number[],
) => {
  const old = items[oldIndexPath[0]];
  items.splice(oldIndexPath[0], 1);
  const container = items[newIndexPath[0]];
  container.children.splice(newIndexPath[1] + 1, 0, old);
  return items;
};

function App() {
  const setSchemas = useRendererStore((state) => state.setSchemas);
  const getSchemas = useRendererStore((state) => state.getSchemas);
  const setSchemaIds = useRendererStore((state) => state.setSchemaIds);
  const getSchemaIds = useRendererStore((state) => state.getSchemaIds);

  const handleSortDataChange = (
    schemas: Schema[],
    schemaIds: (string | ContainerID)[],
    active: Active,
    over: Over,
  ) => {
    const oldIndexPath = getPathById(
      schemaIds,
      active.id as string,
      active.data.current?.sortType,
    );
    const newIndexPath = getPathById(
      schemaIds,
      over.id as string,
      over.data.current?.sortType,
    );

    if (
      (over.data.current?.sortType as string).startsWith('container') &&
      !(active.data.current?.sortType as string).includes('container')
    ) {
      if (newIndexPath[0] === -1) return;
      if ((schemaIds[newIndexPath[0]] as ContainerID).children.length === 0) {
        const newIds = [...schemaIds];
        (newIds[newIndexPath[0]] as ContainerID).children.push(active.id as string);
        newIds.splice(oldIndexPath[0], 1);
        setSchemaIds(newIds);

        const newSchemas = [...schemas];
        (newSchemas[newIndexPath[0]] as RowSchema).children.push(
          newSchemas[oldIndexPath[0]] as FormItemSchema,
        );
        newSchemas.splice(oldIndexPath[0], 1);
        setSchemas(newSchemas);
        return;
      }
    }

    // 有一方的index 为-1，说明是子项
    // 如果oldIndex 为-1，说明是拖拽子项到外部
    if (oldIndexPath.length > 1 && newIndexPath.length === 1) {
      setSchemaIds(moveContainerItemToRoot(schemaIds, newIndexPath, oldIndexPath));
      setSchemas(moveContainerItemToRoot(schemas, newIndexPath, oldIndexPath));
    }
    // 如果newIndex 为-1，说明是拖拽子项到容器内部
    else if (oldIndexPath.length === -1 && newIndexPath.length > 1) {
      setSchemaIds(moveItemToContainer(schemaIds, newIndexPath, oldIndexPath));
      setSchemas(moveItemToContainer(schemas, newIndexPath, oldIndexPath));
    }
    // 如果都为-1，则说明是容器内部互相移动
    else if (oldIndexPath.length > 1 && newIndexPath.length > 1) {
      // moveContainerItemToOtherContainer(
      //   items,
      //   active.id as string,
      //   over.id as string
      // );
    } else {
      setSchemaIds(arrayMove(schemaIds, oldIndexPath[0], newIndexPath[0]));
      setSchemas(arrayMove(schemas, oldIndexPath[0], newIndexPath[0]));
    }
  };

  const onDragEnd = (e: DragEndEvent) => {
    const { active, over } = e;
    const schemas = getSchemas();
    const schemaIds = getSchemaIds();
    const schema = defaultSchema[active.id];
    console.log(e);

    if (active.id === over?.id) return;

    const newId =
      active.data.current?.sortType === 'container'
        ? {
            id: active.id + `-${uuid().slice(0, 8)}`,
            children: [],
          }
        : active.id + `-${uuid().slice(0, 8)}`;

    if (over?.id === 'droppable') {
      if (!schemas.length) {
        const newSchema = { ...schema };
        newSchema.properties.name = typeof newId === 'object' ? newId.id : newId;
        schemas.push(schema);
        setSchemas(schemas);
        setSchemaIds([newId]);
      }
    } else if (over?.id) {
      if (active.data.current?.properties) {
        handleSortDataChange(schemas, schemaIds, active, over);
      } else {
        const idPath = getPathById(
          schemaIds,
          over.id as string,
          over.data.current?.sortType,
        );

        setSchemaIds(updateSchemaIds(schemaIds, idPath, newId));
        setSchemas(updateSchemas(schemas, idPath, schema));
      }
    }
  };
  return (
    <DndContext onDragEnd={onDragEnd} collisionDetection={customCollisionDetection}>
      <Layout sidebarContent={<SideContent />} headerContent={<HeaderContent />}>
        <MainContent />
      </Layout>
    </DndContext>
  );
}

export default App;
