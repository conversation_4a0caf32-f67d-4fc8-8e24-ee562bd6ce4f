import type { DragEndEvent, DragStartEvent } from '@dnd-kit/core';
import { DndContext, useDroppable } from '@dnd-kit/core';
import './drop-board.less';
import { SortableContext } from '@dnd-kit/sortable';
import classNames from 'classnames';

import { useRendererStore } from '@/store';

interface DropBoardProps {
  children: React.ReactNode;
}

const DropBoard = () => {
  const schemaIds = useRendererStore((state) => state.schemaIds);
  const { isOver, setNodeRef } = useDroppable({
    id: 'droppable',
  });
  return (
    <div className="drop-container" ref={setNodeRef}>
      <div className={classNames('empty', { 'is-over': isOver })}>拖拽组件到这里</div>
    </div>
  );
};

export default DropBoard;
