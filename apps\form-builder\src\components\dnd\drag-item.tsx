import type { ClientRect } from '@dnd-kit/core';
import { useDraggable } from '@dnd-kit/core';
import { SortableContext, useSortable } from '@dnd-kit/sortable';
import { Col, Row } from 'antd';
import type { FormInstance } from 'antd';
import classNames from 'classnames';
import { debounce } from 'lodash-es';
import { renderFormItem } from 'pb-form-render';
import type { RowSchema } from 'pb-form-render/dist/typing/types';
import type { MouseEventHandler } from 'react';
import React, { cloneElement, useEffect, useState } from 'react';

import { useRendererStore } from '@/store';
import './drag-item.less';

export type ContainerID = { id: string; children: string[] };

export function DragItem(props: {
  id: string;
  type: string;
  children: React.ReactNode;
}) {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: props.id,
    data: {
      sortType: props.type,
    },
  });

  return (
    <div ref={setNodeRef} {...listeners} {...attributes}>
      {props.children}
    </div>
  );
}

export const RowDropItem = (props: { isOver?: boolean }) => {
  return (
    <div className={classNames('row-drop-item', { 'is-over': props.isOver })}>
      <div className="title">行容器</div>
      {props.isOver}
      <div className="desc">拖拽组件到这里</div>
    </div>
  );
};

export const DragTool = () => {
  const { detectingId, activeId, setActiveId, setDetectingId } = useRendererStore();

  const [rect, setRect] = useState({ width: 0, height: 0, top: 0, left: 0 });
  const setDectectingRect = debounce((id: string) => {
    const ele = document.querySelector(`[data-component-id="${id}"]`);
    const boundingRect = ele?.getBoundingClientRect();
    const containerRect = document
      .querySelector('.content-container')
      ?.getBoundingClientRect();
    const newRect = {
      top: (boundingRect?.top || 0) - (containerRect?.top || 0) - 20,
      left: (boundingRect?.left || 0) - (containerRect?.left || 0) - 20,
      width: boundingRect?.width || 0,
      height: boundingRect?.height || 0,
    };
    console.log(id, boundingRect, containerRect, newRect, 'rect2');
    setRect(newRect);
  }, 20);
  useEffect(() => {
    setDectectingRect(detectingId);
  }, [detectingId]);

  return (
    detectingId &&
    detectingId !== activeId && (
      <div
        style={{
          position: 'absolute',
          width: rect.width,
          height: rect.height,
          left: rect.left,
          top: rect.top,
          border: '1px solid red',
        }}
        onClick={() => {
          console.log(detectingId);
          setActiveId(detectingId);
          setDetectingId('');
        }}
      ></div>
    )
  );
};

export const SortableItem = ({
  id,
  data,
  children,
}: {
  id: string;
  data: Record<string, any>;
  children: React.ReactElement;
}) => {
  console.log(id, data, 'sortid');
  const { over, transform, attributes, listeners, isDragging, isOver, setNodeRef } =
    useSortable({
      id,
      data,
    });
  const { activeId, setActiveId, detectingId, setDetectingId } = useRendererStore();
  console.log(id, 'render sortitem');

  const handleClick: MouseEventHandler = (e) => {
    console.log(id, e.currentTarget, e.target, 'idddd');
    e.stopPropagation();
    e.preventDefault();
    if (activeId === id) return;
    setActiveId(id);
  };

  const deactiveElement = (
    <div
      ref={setNodeRef}
      {...attributes}
      className="sortable-item"
      data-component-id={id}
      style={
        {
          boxSizing: 'content-box',
          opacity: isDragging ? 0.3 : 1,
          border: activeId === id ? '1px solid blue' : 'none',
        } as React.CSSProperties
      }
      onMouseOver={(e) => {
        if (data.sortType === 'rowFormItem') {
          console.log(id, 'rowFormItem');
        }
        // e.stopPropagation();
        // e.preventDefault();
        // if (isDragging) return;
        // if (id !== activeId) {
        //   setDetectingId(id);
        // } else {
        //   setDetectingId('');
        // }
        // console.log(id, activeId, over, 'over');
      }}
    ></div>
  );

  const newProps =
    activeId === id
      ? {
          ...listeners,
        }
      : {};

  const wrapperChildren = () => {
    switch (data.type) {
      case 'row': {
        if (data.children.length) {
          return children;
        } else {
          return <RowDropItem isOver={isOver} />;
        }
      }
      default:
        return children;
    }
  };

  const element = cloneElement(
    deactiveElement,
    newProps,
    <div>
      {isOver && <div className="sortable-over-indicator"></div>}
      {wrapperChildren()}
    </div>,
  );
  return element;
};

export const SortableRowItem = (props: {
  id: string;
  data: RowSchema & { sortType: string };
  form: FormInstance;
}) => {
  const { id, data, form } = props;
  const schemaIds = useRendererStore((state) => state.schemaIds);

  const [rowIds, setRowIds] = useState<string[]>(
    (schemaIds.find((item) => (item as ContainerID).id === id) as ContainerID)
      .children || [],
  );
  console.log(rowIds, 'rowIds');

  useEffect(() => {
    setRowIds(
      (schemaIds.find((item) => (item as ContainerID).id === id) as ContainerID)
        .children || [],
    );
  }, [schemaIds]);
  return (
    <SortableItem id={id} data={data}>
      <SortableContext items={rowIds}>
        <Row {...data.properties}>
          {rowIds.map((rowId, index) => (
            <Col span={24 / data.properties.colNum} key={rowId}>
              <SortableItem
                key={rowId}
                id={rowId}
                data={{ ...data.children[index], sortType: 'rowFormItem' }}
              >
                {renderFormItem({ schema: data.children[index], form })}
              </SortableItem>
            </Col>
          ))}
        </Row>
      </SortableContext>
    </SortableItem>
  );
};
