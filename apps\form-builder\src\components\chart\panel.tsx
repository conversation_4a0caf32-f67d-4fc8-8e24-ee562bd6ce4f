import { DeleteOutlined, CopyOutlined } from '@ant-design/icons';
import type { ItemConfig } from 'pb-bi-render';
import { useMemo } from 'react';

import DragItem from '../dnd/drag-item';
import './panel.less';

interface IPanelProps {
  id: string;
  selectedId: string;
  chartDragging?: boolean;
  children: React.ReactNode;
  chartConfig: ItemConfig;
  onSelect: (id: string) => void;
  onDelete?: (id: string) => void;
  onCopy?: (id: string) => void;
}

const ChartPanel: React.FC<IPanelProps> = (props: IPanelProps) => {
  const { children, id, selectedId, onSelect, onCopy, onDelete, chartConfig } = props;
  const isSelected = useMemo(() => id === selectedId, [id, selectedId]);

  const { row, col, rowSpan, colSpan, itemBackground } = chartConfig;
  console.log(chartConfig, 'chartconfig');
  const gridStyle = useMemo(
    () => ({
      gridRow: rowSpan ? `${row} / span ${rowSpan}` : row,
      gridColumn: colSpan ? `${col} / span ${colSpan}` : col,
      background: itemBackground,
    }),
    [row, col, rowSpan, colSpan, itemBackground],
  );
  return (
    <DragItem type="chart" item={{ key: id }} style={{ ...gridStyle }}>
      <div
        className={`chart-panel ${isSelected ? 'panel-selected' : ''}`}
        onMouseDown={(e) => {
          e.stopPropagation();
          onSelect(id);
        }}
      >
        {isSelected && (
          <div className="icons">
            <div
              onClick={(e) => {
                e.stopPropagation();
                onCopy?.(id);
              }}
            >
              <CopyOutlined />
            </div>
            <div
              onClick={(e) => {
                e.stopPropagation();
                onDelete?.(id);
              }}
            >
              <DeleteOutlined />
            </div>
          </div>
        )}
        {children}
      </div>
    </DragItem>
  );
};

export default ChartPanel;
