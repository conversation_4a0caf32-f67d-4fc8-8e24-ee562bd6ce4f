{"name": "form-builder", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint:js": "eslint src/**/*.{js,jsx,ts,tsx}", "lint:css": "stylelint --allow-empty-input 'src/**/*.{css,less}'", "lint:format": "prettier --check *.md *.json src/**/*.{js,jsx,ts,tsx,css,less,md,json}", "lint:typing": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@pb-renders/utils": "workspace:^", "antd": "^5.22.6", "classnames": "^2.5.1", "lodash-es": "^4.17.21", "pb-form-render": "workspace:^", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "uuid": "^11.0.3", "zustand": "^5.0.2"}, "devDependencies": {"@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "globals": "^15.13.0", "less": "^4.2.1", "vite": "^5.4.11"}}