{"name": "pb-renders", "version": "1.0.0", "description": "", "private": true, "scripts": {"preinstall": "husky install", "dev": "nx run-many --target=dev", "build": "nx run-many --target=build", "test": "echo \"Error: no test specified\" && exit 1", "lint": "nx run-many --target=lint:js,lint:css,lint:format"}, "packageManager": "pnpm@9.7.3", "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@pb-renders/config-eslint": "workspace:^", "@pb-renders/config-stylelint": "workspace:^", "eslint": "^8.35.0", "husky": "^9.1.7", "jest": "^29.5.0", "lint-staged": "^13.1.2", "nx": "^20.3.0", "prettier": "^2.8.4", "stylelint": "^15.2.0", "tsup": "^6.7.0", "typescript": "^4.9.5"}}