{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["ESNext", "DOM", "DOM.Iterable"], "downlevelIteration": true, "module": "ESNext", "moduleResolution": "node", "noImplicitReturns": true, "noUnusedLocals": true, "preserveWatchOutput": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ESNext"}, "exclude": ["node_modules"]}