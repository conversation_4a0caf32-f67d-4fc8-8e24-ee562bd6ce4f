import type { Schema } from 'pb-form-render';
import { SchemaType, FieldCategory } from 'pb-form-render';

export const defaultSchema: Record<string, Schema> = {
  input: {
    type: SchemaType.FORM_ITEM,
    properties: {
      label: '用户名',
      category: FieldCategory.INPUT,
      name: 'username',
      props: {
        placeholder: '请输入',
      },
      itemProps: {
        required: true,
        labelCol: {
          span: 4,
        },
      },
    },
  },
  select: {
    type: SchemaType.FORM_ITEM,
    properties: {
      label: '下拉框',
      category: FieldCategory.SELECT,
      name: 'username',
      props: {
        placeholder: '请选择',
      },
      itemProps: {
        required: true,
        labelCol: {
          span: 8,
        },
      },
    },
  },
  row: {
    type: SchemaType.ROW,
    properties: {
      name: 'row1',
      colNum: 3,
    },
    children: [],
  },
};
